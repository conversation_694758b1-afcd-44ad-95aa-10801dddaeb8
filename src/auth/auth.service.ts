import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CreateUserDto } from 'src/users/dto/create-user.dto';
import { User } from 'src/users/entities/user.entity';
import { Repository } from 'typeorm';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User) private readonly usersRepo: Repository<User>,
  ) {}

  async validateGoogle(dto: CreateUserDto) {
    const { email } = dto;

    const user = await this.usersRepo.findOneBy({ email });

    if (user)
      return await this.usersRepo.update(
        { id: user.id },
        {
          firstName: dto.firstName,
          lastName: dto.lastName,
          picture: dto.picture,
        },
      );

    const newUser = this.usersRepo.create(dto);
    return await this.usersRepo.save(newUser);
  }

  async findUser(id: string) {
    return await this.usersRepo.findOneBy({ id });
  }

  google() {
    return { msg: 'Google Auth route' };
  }

  googleCallback() {
    return { msg: 'Google Auth callback/redirect route' };
  }
}
