import { Injectable } from '@nestjs/common';
import { AuthService } from 'src/auth/auth.service';
import { User } from 'src/users/entities/user.entity';

@Injectable()
export class SessionSerializer {
  constructor(private readonly authService: AuthService) {}

  serializeUser(user: User, done: (err: any, user?: any) => void) {
    done(null, user);
  }

  async deserializeUser(payload: User, done: (err: any, user?: any) => void) {
    const user = await this.authService.findUser(payload.id);
    return user ? done(null, user) : done(null, null);
  }
}
