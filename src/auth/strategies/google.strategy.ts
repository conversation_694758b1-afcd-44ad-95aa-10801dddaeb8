import { Inject, Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Profile, Strategy } from 'passport-google-oauth20';
import { AuthService } from 'src/auth/auth.service';
import { ApiConfig } from 'src/env.schema';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy) {
  constructor(
    @Inject(ApiConfig) private readonly config: ApiConfig,
    private readonly authService: AuthService,
  ) {
    super({
      clientID: config.GOOGLE_CLIENT_ID,
      clientSecret: config.GOOGLE_CLIENT_SECRET,
      callbackURL: config.GOOGLE_CALLBACK_URL,
      scope: ['email', 'profile'],
    });
  }

  async validate(accessToken: string, refreshToken: string, profile: Profile) {
    const { given_name, family_name, email, picture } = profile._json;

    const gUser = {
      email: email || '',
      firstName: given_name || '',
      lastName: family_name || '',
      picture,
      accessToken,
      refreshToken,
    };

    const user = await this.authService.validateGoogle(gUser);
    return user || null;
  }
}
