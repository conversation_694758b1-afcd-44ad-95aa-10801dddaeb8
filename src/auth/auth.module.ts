import { Module } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GoogleStrategy } from 'src/auth/strategies/google.strategy';
import { SessionSerializer } from 'src/auth/strategies/serializer';
import { ApiConfig, Env } from 'src/env.schema';
import { User } from 'src/users/entities/user.entity';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

@Module({
  imports: [PassportModule, TypeOrmModule.forFeature([User])],
  controllers: [AuthController],
  providers: [
    AuthService,
    GoogleStrategy,
    SessionSerializer,
    {
      provide: ApiConfig,
      useValue: Env,
    },
  ],
  exports: [AuthService],
})
export class AuthModule {}
