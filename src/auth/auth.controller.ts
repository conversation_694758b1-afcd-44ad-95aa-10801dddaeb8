import { Controller, Get, UseGuards } from '@nestjs/common';
import { GoogleAuthGuard } from 'src/auth/guards/google.guard';
import { AuthService } from './auth.service';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}
  @Get('google')
  @UseGuards(GoogleAuthGuard)
  google() {
    return this.authService.google();
  }

  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  googleCallback() {
    return this.authService.googleCallback();
  }
}
