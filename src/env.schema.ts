import * as dotenv from 'dotenv';
import { z } from 'zod';
import pkg from '../package.json';

dotenv.config();

const API_NAME = pkg?.name.split('-').join(' ').toUpperCase();
const API_DESCRIPTION = pkg?.description;
const API_VERSION = pkg?.version;
const API_AUTHOR = pkg?.author;

const envSchema = z.object({
  NODE_ENV: z
    .enum(['development', 'test', 'production'])
    .default('development'),
  PORT: z.coerce.number(),
  API_NAME: z.string().min(1).default(API_NAME),
  API_DESCRIPTION: z.string().min(1).default(API_DESCRIPTION),
  API_VERSION: z.string().min(1).default(API_VERSION),
  API_AUTHOR: z.string().min(1).default(API_AUTHOR),
  API_PREFIX: z.string().min(1).default('api/v1'),

  DB_URL: z.string().min(1),
  // REDIS_HOST: z.string().min(1),
  // REDIS_PORT: z.coerce.number().min(1),
  // REDIS_PASSWORD: z.string().min(1),

  SESSION_SECRET: z.string().min(1),
  // JWT_SECRET: z.string().min(1),
  // JWT_REFRESH_SECRET: z.string().min(1),
  // JWT_TTL: z.string().min(1),
  // JWT_REFRESH_TTL: z.string().min(1),

  GOOGLE_CLIENT_ID: z.string().min(1),
  GOOGLE_CLIENT_SECRET: z.string().min(1),
  GOOGLE_CALLBACK_URL: z.string().min(1),

  // THROTTLER_TTL: z.coerce.number().min(1),
  // THROTTLER_LIMIT: z.coerce.number().min(1),

  // CLOUDFLARE_API_TOKEN: z.string().min(1),
  // ANSIBLE_PLAYBOOK_PATH: z.string(),
  // SSH_PRIVATE_KEY_PATH: z.string(),

  // ENCRYPTION_KEY: z.string().min(1),
  // FRONTEND_URL: z.string().min(1),
});

export type ApiConfig = z.infer<typeof envSchema>;
export const Env: ApiConfig = envSchema.parse(process.env);
export const ApiConfig = 'API_CONFIG';
