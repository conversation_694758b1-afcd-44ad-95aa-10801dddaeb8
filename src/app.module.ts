import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { dbConfig } from 'src/db.config';
import { Env } from 'src/env.schema';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => Env],
    }),
    TypeOrmModule.forRoot(dbConfig),
    PassportModule.register({ session: true }),
    AuthModule,
    UsersModule,
  ],
})
export class AppModule {}
