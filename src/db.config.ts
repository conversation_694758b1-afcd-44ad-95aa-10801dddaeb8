import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { Env } from 'src/env.schema';
import { DataSource, DataSourceOptions } from 'typeorm';

export const dbConfig: DataSourceOptions & TypeOrmModuleOptions = {
  type: 'postgres',
  url: Env.DB_URL,
  entities: ['dist/src/domain/**/*.entity.js'],
  migrations: ['dist/src/migrations/*.js'],
  synchronize: true,
  autoLoadEntities: true,
};

export const dataSource = new DataSource(dbConfig);
